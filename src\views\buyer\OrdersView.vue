<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON>er -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-6">
          <h1 class="text-3xl font-bold text-gray-900">My Orders</h1>
          <p class="mt-2 text-gray-600">Track your gas delivery orders</p>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Loading State -->
      <div v-if="loading" class="text-center py-12">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <p class="mt-2 text-gray-600">Loading orders...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="text-center py-12">
        <div class="text-red-600 mb-4">
          <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Error Loading Orders</h3>
        <p class="text-gray-600 mb-4">{{ error }}</p>
        <button @click="loadOrders" class="btn-primary">Try Again</button>
      </div>

      <!-- Orders List -->
      <div v-else class="space-y-6">
        <div
          v-for="order in orders"
          :key="order.id"
          class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
        >
          <div class="p-6">
            <div class="flex items-center justify-between mb-4">
              <div>
                <h3 class="text-lg font-semibold text-gray-900">Order #{{ order.id }}</h3>
                <p class="text-sm text-gray-600">Placed on {{ formatDate(order.created_at) }}</p>
              </div>
              <div class="text-right">
                <span
                  :class="[
                    'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',
                    getStatusColor(order.status)
                  ]"
                >
                  {{ getStatusLabel(order.status) }}
                </span>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              <div>
                <p class="text-sm font-medium text-gray-700">Product</p>
                <p class="text-sm text-gray-900">{{ order.gas_details.brand }} - {{ order.gas_details.weight_kg }} KG</p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-700">Seller</p>
                <p class="text-sm text-gray-900">{{ order.seller_name }}</p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-700">Total Amount</p>
                <p class="text-sm text-gray-900">RWF {{ order.total_price.toLocaleString() }}</p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-700">Delivery Address</p>
                <p class="text-sm text-gray-900">{{ order.delivery_address }}</p>
              </div>
            </div>

            <div class="flex items-center justify-between pt-4 border-t border-gray-200">
              <div class="flex space-x-3">
                <button
                  v-if="order.status === 'DELIVERED'"
                  @click="rateOrder(order.id)"
                  class="text-sm text-primary-600 hover:text-primary-700 font-medium"
                >
                  Rate Seller
                </button>
                <button
                  class="text-sm text-gray-600 hover:text-gray-700 font-medium"
                >
                  View Details
                </button>
              </div>
              <div class="text-sm text-gray-500">
                Last updated: {{ formatDate(order.updated_at) }}
              </div>
            </div>
          </div>
        </div>
        <!-- Empty State -->
        <div v-if="orders.length === 0" class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No orders yet</h3>
          <p class="mt-1 text-sm text-gray-500">Start by browsing our gas products.</p>
          <div class="mt-6">
            <router-link to="/buyer/dashboard" class="btn-primary">
              Browse Products
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import apiService from '@/services/apiService'

const router = useRouter()

interface Order {
  id: number
  gas_inventory: number
  gas_details: {
    brand: string
    weight_kg: number
    unit_price: number
    location: string
  }
  buyer: number
  buyer_name: string
  seller_name: string
  quantity: number
  total_price: number
  status: string
  delivery_address: string
  contact_phone: string
  created_at: string
  updated_at: string
}

const orders = ref<Order[]>([])
const loading = ref(false)
const error = ref('')

// Load orders from API
const loadOrders = async () => {
  loading.value = true
  error.value = ''

  try {
    const data = await apiService.getMyOrders()
    orders.value = data
  } catch (err: any) {
    error.value = err.message || 'Failed to load orders'
    console.error('Error loading orders:', err)
  } finally {
    loading.value = false
  }
}

const getStatusColor = (status: string) => {
  const colors = {
    'PENDING': 'bg-yellow-100 text-yellow-800',
    'APPROVED': 'bg-blue-100 text-blue-800',
    'DELIVERED': 'bg-green-100 text-green-800',
    'REJECTED': 'bg-red-100 text-red-800',
    'CANCELLED': 'bg-red-100 text-red-800'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

const getStatusLabel = (status: string) => {
  const labels = {
    'PENDING': 'Pending Admin Approval',
    'APPROVED': 'Approved',
    'DELIVERED': 'Delivered',
    'REJECTED': 'Rejected',
    'CANCELLED': 'Cancelled'
  }
  return labels[status as keyof typeof labels] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const rateOrder = (orderId: number) => {
  router.push({ name: 'buyer-rating', params: { id: orderId.toString() } })
}

onMounted(() => {
  loadOrders()
})
</script>
