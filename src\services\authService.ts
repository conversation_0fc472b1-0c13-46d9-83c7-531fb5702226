import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'

// Create axios instance for auth
const authClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000,
})

// Add request interceptor to include auth token
authClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

export const authService = {
  async login(email: string, password: string) {
    const response = await authClient.post('/accounts/login/', {
      email,
      password
    })
    return response
  },

  async register(email: string, password: string, confirmPassword: string, role: 'buyer' | 'seller') {
    const response = await authClient.post('/accounts/register/', {
      email,
      password,
      confirm_password: confirmPassword,
      role
    })
    return response
  },

  async logout() {
    const response = await authClient.post('/accounts/logout/')
    return response
  },

  async refreshToken(refreshToken: string) {
    const response = await authClient.post('/accounts/token/refresh/', {
      refresh: refreshToken
    })
    return response
  }
}
