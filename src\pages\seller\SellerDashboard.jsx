import React from 'react'
import Layout from '../../components/Layout'

const SellerDashboard = () => {
  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Seller Dashboard</h1>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900">Total Inventory</h3>
            <p className="text-3xl font-bold text-blue-600 mt-2">0</p>
          </div>
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900">Pending Orders</h3>
            <p className="text-3xl font-bold text-yellow-600 mt-2">0</p>
          </div>
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900">Total Sales</h3>
            <p className="text-3xl font-bold text-green-600 mt-2">RWF 0</p>
          </div>
        </div>
        <div className="bg-white shadow rounded-lg p-6">
          <p className="text-gray-600">Seller dashboard content will be implemented here.</p>
        </div>
      </div>
    </Layout>
  )
}

export default SellerDashboard
