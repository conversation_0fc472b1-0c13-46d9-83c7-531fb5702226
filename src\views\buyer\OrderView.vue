<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-6">
          <h1 class="text-3xl font-bold text-gray-900">Place Order</h1>
          <p class="mt-2 text-gray-600">Review your order and provide delivery details</p>
        </div>
      </div>
    </div>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Order Summary -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Order Summary</h2>
          
          <div class="space-y-4">
            <div v-for="item in orderItems" :key="item.id" class="flex items-center justify-between border-b border-gray-200 pb-4">
              <div>
                <h3 class="font-medium text-gray-900">{{ item.brand }} - {{ item.weight }}</h3>
                <p class="text-sm text-gray-600">{{ item.sellerName }}</p>
              </div>
              <div class="text-right">
                <p class="font-medium text-gray-900">RWF {{ item.price.toLocaleString() }}</p>
              </div>
            </div>
          </div>

          <div class="mt-6 border-t border-gray-200 pt-4">
            <div class="flex justify-between text-lg font-semibold text-gray-900">
              <span>Total</span>
              <span>RWF {{ totalAmount.toLocaleString() }}</span>
            </div>
          </div>
        </div>

        <!-- Order Form -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Delivery Information</h2>
          
          <form @submit.prevent="placeOrder" class="space-y-4">
            <div>
              <label for="fullName" class="block text-sm font-medium text-gray-700">
                Full Name <span class="text-red-500">*</span>
              </label>
              <input
                id="fullName"
                v-model="orderForm.fullName"
                type="text"
                required
                class="input-field mt-1"
                placeholder="Enter your full name"
              />
            </div>

            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700">
                Contact Phone <span class="text-red-500">*</span>
              </label>
              <input
                id="phone"
                v-model="orderForm.phone"
                type="tel"
                required
                class="input-field mt-1"
                placeholder="Enter your phone number"
              />
            </div>

            <div>
              <label for="address" class="block text-sm font-medium text-gray-700">
                Delivery Address <span class="text-red-500">*</span>
              </label>
              <textarea
                id="address"
                v-model="orderForm.address"
                rows="3"
                required
                class="input-field mt-1"
                placeholder="Enter your complete delivery address"
              ></textarea>
            </div>

            <div>
              <label for="district" class="block text-sm font-medium text-gray-700">
                District <span class="text-red-500">*</span>
              </label>
              <select
                id="district"
                v-model="orderForm.district"
                required
                class="input-field mt-1"
              >
                <option value="">Select district</option>
                <option value="Gasabo">Gasabo</option>
                <option value="Kicukiro">Kicukiro</option>
                <option value="Nyarugenge">Nyarugenge</option>
              </select>
            </div>

            <div class="flex items-center">
              <input
                id="saveAddress"
                v-model="orderForm.saveAddress"
                type="checkbox"
                class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label for="saveAddress" class="ml-2 block text-sm text-gray-900">
                Save address for future use
              </label>
            </div>

            <!-- Payment Method -->
            <div class="mt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Method</h3>
              
              <div class="space-y-3">
                <div class="flex items-center">
                  <input
                    id="momo"
                    v-model="orderForm.paymentMethod"
                    value="momo"
                    type="radio"
                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                  />
                  <label for="momo" class="ml-3 block text-sm font-medium text-gray-700">
                    Mobile Money (MTN MoMo, Airtel Money)
                  </label>
                </div>

                <div class="flex items-center">
                  <input
                    id="card"
                    v-model="orderForm.paymentMethod"
                    value="card"
                    type="radio"
                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                  />
                  <label for="card" class="ml-3 block text-sm font-medium text-gray-700">
                    Credit/Debit Card
                  </label>
                </div>

                <div class="flex items-center">
                  <input
                    id="bank"
                    v-model="orderForm.paymentMethod"
                    value="bank"
                    type="radio"
                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                  />
                  <label for="bank" class="ml-3 block text-sm font-medium text-gray-700">
                    Bank Transfer
                  </label>
                </div>
              </div>

              <!-- Payment Details -->
              <div v-if="orderForm.paymentMethod === 'momo'" class="mt-4">
                <label for="momoPhone" class="block text-sm font-medium text-gray-700">
                  Mobile Money Phone Number
                </label>
                <input
                  id="momoPhone"
                  v-model="orderForm.momoPhone"
                  type="tel"
                  class="input-field mt-1"
                  placeholder="Enter your mobile money number"
                />
              </div>

              <div v-if="orderForm.paymentMethod === 'card'" class="mt-4 space-y-3">
                <div>
                  <label for="cardNumber" class="block text-sm font-medium text-gray-700">
                    Card Number
                  </label>
                  <input
                    id="cardNumber"
                    v-model="orderForm.cardNumber"
                    type="text"
                    class="input-field mt-1"
                    placeholder="1234 5678 9012 3456"
                  />
                </div>
                <div class="grid grid-cols-2 gap-3">
                  <div>
                    <label for="expiryDate" class="block text-sm font-medium text-gray-700">
                      Expiry Date
                    </label>
                    <input
                      id="expiryDate"
                      v-model="orderForm.expiryDate"
                      type="text"
                      class="input-field mt-1"
                      placeholder="MM/YY"
                    />
                  </div>
                  <div>
                    <label for="cvv" class="block text-sm font-medium text-gray-700">
                      CVV
                    </label>
                    <input
                      id="cvv"
                      v-model="orderForm.cvv"
                      type="text"
                      class="input-field mt-1"
                      placeholder="123"
                    />
                  </div>
                </div>
              </div>

              <div v-if="orderForm.paymentMethod === 'bank'" class="mt-4 p-4 bg-gray-50 rounded-lg">
                <h4 class="font-medium text-gray-900 mb-2">Bank Transfer Details</h4>
                <div class="text-sm text-gray-600 space-y-1">
                  <p><strong>Bank:</strong> Bank of Kigali</p>
                  <p><strong>Account Name:</strong> Kigali GasGo Ltd</p>
                  <p><strong>Account Number:</strong> *********</p>
                  <p><strong>Reference:</strong> Your order ID will be provided</p>
                </div>
              </div>
            </div>

            <!-- Error message -->
            <div v-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
              <p class="text-sm text-red-800">{{ error }}</p>
            </div>

            <!-- Submit Button -->
            <div class="mt-6">
              <button
                type="submit"
                :disabled="loading"
                class="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span v-if="loading">Processing...</span>
                <span v-else>Place Order & Pay (RWF {{ totalAmount.toLocaleString() }})</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

interface OrderItem {
  id: string
  brand: string
  weight: string
  price: number
  sellerName: string
}

// Mock order items - in real app, this would come from cart/state
const orderItems = ref<OrderItem[]>([
  {
    id: '1',
    brand: 'Meru',
    weight: '12 KG',
    price: 15000,
    sellerName: 'Kigali Gas Supplies'
  }
])

const loading = ref(false)
const error = ref('')

const orderForm = reactive({
  fullName: '',
  phone: '',
  address: '',
  district: '',
  saveAddress: false,
  paymentMethod: 'momo',
  momoPhone: '',
  cardNumber: '',
  expiryDate: '',
  cvv: ''
})

const totalAmount = computed(() => {
  return orderItems.value.reduce((total, item) => total + item.price, 0)
})

const placeOrder = async () => {
  if (!orderForm.fullName || !orderForm.phone || !orderForm.address || !orderForm.district) {
    error.value = 'Please fill in all required fields'
    return
  }

  if (!orderForm.paymentMethod) {
    error.value = 'Please select a payment method'
    return
  }

  loading.value = true
  error.value = ''

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Redirect to orders page
    router.push({ name: 'buyer-orders' })
  } catch (err) {
    error.value = 'Failed to place order. Please try again.'
  } finally {
    loading.value = false
  }
}
</script>
