# GasGo Rwanda

A React-based gas stock management application for Rwanda, connecting gas buyers and sellers with admin oversight.

## Tech Stack

- React 18 with Vite
- React Router v6 for routing
- Tailwind CSS for styling
- Axios for API integration
- React Hook Form for form handling

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) with the following extensions:

- ES7+ React/Redux/React-Native snippets
- Tailwind CSS IntelliSense
- ESLint

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```

### Lint with [ESLint](https://eslint.org/)

```sh
npm run lint
```
