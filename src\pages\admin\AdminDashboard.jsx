import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import Layout from '../../components/Layout'
import apiService from '../../services/apiService'

const AdminDashboard = () => {
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeSellers: 0,
    pendingOrders: 0,
    pendingInvoices: 0,
    totalRevenue: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    fetchDashboardStats()
  }, [])

  const fetchDashboardStats = async () => {
    try {
      setLoading(true)
      const dashboardData = await apiService.getDashboardStats()
      setStats(dashboardData)
    } catch (err) {
      setError(err.message || 'Failed to fetch dashboard statistics')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="mt-2 text-gray-600">Welcome back, Admin</p>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Link
            to="/admin/orders"
            className="bg-blue-600 text-white p-6 rounded-lg hover:bg-blue-700 transition-colors"
          >
            <h3 className="text-lg font-semibold">Manage Orders</h3>
            <p className="mt-2 text-blue-100">Review and approve orders</p>
          </Link>

          <Link
            to="/admin/invoices"
            className="bg-green-600 text-white p-6 rounded-lg hover:bg-green-700 transition-colors"
          >
            <h3 className="text-lg font-semibold">Manage Invoices</h3>
            <p className="mt-2 text-green-100">Review and approve invoices</p>
          </Link>

          <Link
            to="/admin/users"
            className="bg-purple-600 text-white p-6 rounded-lg hover:bg-purple-700 transition-colors"
          >
            <h3 className="text-lg font-semibold">Manage Users</h3>
            <p className="mt-2 text-purple-100">View and manage user accounts</p>
          </Link>

          <Link
            to="/admin/reports"
            className="bg-indigo-600 text-white p-6 rounded-lg hover:bg-indigo-700 transition-colors"
          >
            <h3 className="text-lg font-semibold">View Reports</h3>
            <p className="mt-2 text-indigo-100">Analytics and insights</p>
          </Link>
        </div>

        {/* Stats Grid */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">Platform Statistics</h2>
          </div>

          {loading ? (
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading statistics...</p>
            </div>
          ) : error ? (
            <div className="p-6 text-center text-red-600">
              <p>{error}</p>
              <button
                onClick={fetchDashboardStats}
                className="mt-2 text-blue-600 hover:text-blue-800"
              >
                Try again
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 p-6">
              <div className="text-center">
                <p className="text-3xl font-bold text-blue-600">{stats.totalUsers}</p>
                <p className="text-gray-600">Total Users</p>
              </div>
              <div className="text-center">
                <p className="text-3xl font-bold text-green-600">{stats.activeSellers}</p>
                <p className="text-gray-600">Active Sellers</p>
              </div>
              <div className="text-center">
                <p className="text-3xl font-bold text-yellow-600">{stats.pendingOrders}</p>
                <p className="text-gray-600">Pending Orders</p>
              </div>
              <div className="text-center">
                <p className="text-3xl font-bold text-orange-600">{stats.pendingInvoices}</p>
                <p className="text-gray-600">Pending Invoices</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </Layout>
  )
}

export default AdminDashboard
