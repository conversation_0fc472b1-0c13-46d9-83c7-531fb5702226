import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import Layout from '../../components/Layout'
import apiService from '../../services/apiService'

const AdminDashboard = () => {
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeSellers: 0,
    pendingOrders: 0,
    pendingInvoices: 0,
    totalRevenue: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    fetchDashboardStats()
  }, [])

  const fetchDashboardStats = async () => {
    try {
      setLoading(true)
      const dashboardData = await apiService.getDashboardStats()
      setStats(dashboardData)
    } catch (err) {
      setError(err.message || 'Failed to fetch dashboard statistics')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="mt-2 text-gray-600">Manage your gas stock management platform</p>
        </div>

        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
            <button
              onClick={fetchDashboardStats}
              className="ml-2 text-red-800 hover:text-red-900 underline"
            >
              Try again
            </button>
          </div>
        )}

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900">Total Users</h3>
            <p className="text-3xl font-bold text-blue-600 mt-2">{stats.totalUsers}</p>
            <p className="text-sm text-gray-500 mt-1">Registered users</p>
          </div>
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900">Active Sellers</h3>
            <p className="text-3xl font-bold text-green-600 mt-2">{stats.activeSellers}</p>
            <p className="text-sm text-gray-500 mt-1">Verified sellers</p>
          </div>
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900">Pending Orders</h3>
            <p className="text-3xl font-bold text-yellow-600 mt-2">{stats.pendingOrders}</p>
            <p className="text-sm text-gray-500 mt-1">Awaiting approval</p>
          </div>
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900">Pending Invoices</h3>
            <p className="text-3xl font-bold text-orange-600 mt-2">{stats.pendingInvoices}</p>
            <p className="text-sm text-gray-500 mt-1">Awaiting approval</p>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Link
            to="/admin/orders"
            className="bg-blue-600 text-white p-6 rounded-lg hover:bg-blue-700 transition-colors"
          >
            <h3 className="text-lg font-semibold">Manage Orders</h3>
            <p className="mt-2 text-blue-100">Review and approve orders</p>
          </Link>

          <Link
            to="/admin/invoices"
            className="bg-green-600 text-white p-6 rounded-lg hover:bg-green-700 transition-colors"
          >
            <h3 className="text-lg font-semibold">Manage Invoices</h3>
            <p className="mt-2 text-green-100">Review and approve invoices</p>
          </Link>

          <Link
            to="/admin/users"
            className="bg-purple-600 text-white p-6 rounded-lg hover:bg-purple-700 transition-colors"
          >
            <h3 className="text-lg font-semibold">Manage Users</h3>
            <p className="mt-2 text-purple-100">View and manage user accounts</p>
          </Link>

          <Link
            to="/admin/reports"
            className="bg-indigo-600 text-white p-6 rounded-lg hover:bg-indigo-700 transition-colors"
          >
            <h3 className="text-lg font-semibold">View Reports</h3>
            <p className="mt-2 text-indigo-100">Analytics and insights</p>
          </Link>
        </div>

        {/* Revenue Chart Placeholder */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Revenue Overview</h2>
          <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">RWF {stats.totalRevenue?.toLocaleString() || 0}</p>
              <p className="text-gray-600 mt-2">Total Revenue</p>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}

export default AdminDashboard
