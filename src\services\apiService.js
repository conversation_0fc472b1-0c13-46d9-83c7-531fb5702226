// API service for Gas Stock Management Backend
// This handles all API calls to the actual backend server

import axios from 'axios'

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'

// Helper function to handle API errors
const handleApiError = (error, defaultMessage) => {
  return error.response?.data?.detail || error.response?.data?.message || error.message || defaultMessage
}

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000, // 10 seconds timeout
})

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle token refresh
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      const refreshToken = localStorage.getItem('refresh_token')
      if (refreshToken) {
        try {
          const response = await axios.post(`${API_BASE_URL}/v1/token/refresh/`, {
            refresh: refreshToken
          })

          const { access } = response.data
          localStorage.setItem('access_token', access)

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${access}`
          return apiClient(originalRequest)
        } catch (refreshError) {
          // Refresh failed, redirect to login
          localStorage.removeItem('access_token')
          localStorage.removeItem('refresh_token')
          localStorage.removeItem('user')
          window.location.href = '/login'
          return Promise.reject(refreshError)
        }
      }
    }

    return Promise.reject(error)
  }
)

// API Service Methods
const apiService = {
  // Authentication
  async login(username, password) {
    try {
      const response = await apiClient.post('/v1/login/', {
        username,
        password
      })

      const { access, refresh } = response.data

      // Store tokens
      localStorage.setItem('access_token', access)
      localStorage.setItem('refresh_token', refresh)

      // Get user profile
      const userProfile = await this.getCurrentUserProfile()

      return {
        success: true,
        user: userProfile,
        tokens: { access, refresh }
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.detail || error.message || 'Login failed'
      }
    }
  },

  async register(userData) {
    try {
      // Try the v1 endpoint first, then fallback to the main endpoint
      let response
      try {
        response = await apiClient.post('/v1/register/', userData)
      } catch {
        // If v1 fails, try the main register endpoint
        response = await apiClient.post('/register/', userData)
      }

      // Auto-login after successful registration
      const loginResult = await this.login(userData.username, userData.password)

      return {
        success: true,
        user: loginResult.user,
        message: response.data.message
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.detail || error.response?.data?.message || error.message || 'Registration failed'
      }
    }
  },

  // User Profile
  async getCurrentUserProfile() {
    try {
      const response = await apiClient.get('/profiles/me/')
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to get user profile'))
    }
  },

  async getAllUsers() {
    try {
      const response = await apiClient.get('/profiles/')
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to get users'))
    }
  },

  // Gas Inventory
  async getGasInventory(filters) {
    try {
      const params = new URLSearchParams()
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value.toString())
          }
        })
      }

      const response = await apiClient.get(`/v1/gas/?${params.toString()}`)
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to get gas inventory'))
    }
  },

  async getGasInventoryItem(id) {
    try {
      const response = await apiClient.get(`/inventory/${id}/`)
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to get gas inventory item'))
    }
  },

  async createGasInventoryItem(data) {
    try {
      const response = await apiClient.post('/v1/seller/inventory/', data)
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to create gas inventory item'))
    }
  },

  async updateGasInventoryItem(id, data) {
    try {
      const response = await apiClient.patch(`/inventory/${id}/`, data)
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to update gas inventory item'))
    }
  },

  async deleteGasInventoryItem(id) {
    try {
      await apiClient.delete(`/inventory/${id}/`)
      return { success: true }
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to delete gas inventory item'))
    }
  },

  async getMyInventory() {
    try {
      const response = await apiClient.get('/v1/seller/inventory/')
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to get my inventory'))
    }
  },

  // Orders
  async getAllOrders() {
    try {
      const response = await apiClient.get('/orders/')
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to get orders'))
    }
  },

  async getOrder(id) {
    try {
      const response = await apiClient.get(`/orders/${id}/`)
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to get order'))
    }
  },

  async createOrder(data) {
    try {
      const response = await apiClient.post('/v1/orders/', data)
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to create order'))
    }
  },

  async updateOrder(id, data) {
    try {
      const response = await apiClient.patch(`/orders/${id}/`, data)
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to update order'))
    }
  },

  async deleteOrder(id) {
    try {
      await apiClient.delete(`/orders/${id}/`)
      return { success: true }
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to delete order'))
    }
  },

  async getMyOrders() {
    try {
      const response = await apiClient.get('/orders/my_orders/')
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to get my orders'))
    }
  },

  async getSellerOrders() {
    try {
      const response = await apiClient.get('/v1/seller/orders/')
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to get seller orders'))
    }
  },

  async approveOrder(id) {
    try {
      const response = await apiClient.post(`/orders/${id}/approve/`)
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to approve order'))
    }
  },

  async rejectOrder(id) {
    try {
      const response = await apiClient.post(`/orders/${id}/reject/`)
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to reject order'))
    }
  },

  async cancelOrder(id) {
    try {
      const response = await apiClient.post(`/orders/${id}/cancel/`)
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to cancel order'))
    }
  },

  async markOrderAsDelivered(id) {
    try {
      const response = await apiClient.post(`/orders/${id}/mark-delivered/`)
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to mark order as delivered'))
    }
  },

  async getPendingOrders() {
    try {
      const response = await apiClient.get('/v1/admin/orders/pending/')
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to get pending orders'))
    }
  },

  // Invoices
  async getAllInvoices() {
    try {
      const response = await apiClient.get('/invoices/')
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to get invoices'))
    }
  },

  async getInvoice(id) {
    try {
      const response = await apiClient.get(`/invoices/${id}/`)
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to get invoice'))
    }
  },

  async createInvoice(data) {
    try {
      const response = await apiClient.post('/v1/seller/invoice/', data)
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to create invoice'))
    }
  },

  async approveInvoice(id) {
    try {
      const response = await apiClient.post(`/invoices/${id}/approve/`)
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to approve invoice'))
    }
  },

  async markInvoiceAsPaid(id) {
    try {
      const response = await apiClient.post(`/invoices/${id}/mark-as-paid/`)
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to mark invoice as paid'))
    }
  },

  async getPendingInvoices() {
    try {
      const response = await apiClient.get('/v1/admin/invoices/pending/')
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to get pending invoices'))
    }
  },

  // Payments
  async getAllPayments() {
    try {
      const response = await apiClient.get('/payments/')
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to get payments'))
    }
  },

  async getPayment(id) {
    try {
      const response = await apiClient.get(`/payments/${id}/`)
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to get payment'))
    }
  },

  async createPayment(data) {
    try {
      const response = await apiClient.post('/payments/', data)
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to create payment'))
    }
  },

  async updatePayment(id, data) {
    try {
      const response = await apiClient.patch(`/payments/${id}/`, data)
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to update payment'))
    }
  },

  // Ratings
  async getAllRatings() {
    try {
      const response = await apiClient.get('/ratings/')
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to get ratings'))
    }
  },

  async getRating(id) {
    try {
      const response = await apiClient.get(`/ratings/${id}/`)
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to get rating'))
    }
  },

  async createRating(data) {
    try {
      const response = await apiClient.post('/v1/feedback/', data)
      return response.data
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to create rating'))
    }
  },

  // Dashboard Statistics (for admin)
  async getDashboardStats() {
    try {
      // Since there's no specific dashboard endpoint, we'll aggregate data from multiple endpoints
      const [users, orders, invoices, payments] = await Promise.all([
        this.getAllUsers(),
        this.getAllOrders(),
        this.getAllInvoices(),
        this.getAllPayments()
      ])

      // Calculate statistics
      const totalUsers = users.length
      const sellers = users.filter((user) => user.role === 'SELLER')
      const activeSellers = sellers.length

      const pendingOrders = orders.filter((order) => order.status === 'PENDING').length
      const approvedOrders = orders.filter((order) => order.status === 'APPROVED').length
      const deliveredOrders = orders.filter((order) => order.status === 'DELIVERED').length
      const rejectedOrders = orders.filter((order) => order.status === 'REJECTED').length

      const pendingInvoices = invoices.filter((invoice) => !invoice.admin_approval).length

      const totalRevenue = payments
        .filter((payment) => payment.status === 'COMPLETED')
        .reduce((sum, payment) => sum + payment.amount, 0)

      return {
        totalUsers,
        activeSellers,
        totalSellers: sellers.length,
        pendingOrders,
        pendingInvoices,
        totalRevenue,
        orderStats: {
          pending: pendingOrders,
          approved: approvedOrders,
          delivered: deliveredOrders,
          rejected: rejectedOrders
        },
        orders,
        invoices,
        payments
      }
    } catch (error) {
      throw new Error(handleApiError(error, 'Failed to get dashboard statistics'))
    }
  }
}

export default apiService
