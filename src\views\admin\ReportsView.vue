<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex justify-between items-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
        <div class="flex items-center space-x-4">
          <select v-model="selectedPeriod" class="input-field">
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="quarter">This Quarter</option>
            <option value="year">This Year</option>
          </select>
          <button 
            @click="exportReport"
            class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Export
          </button>
        </div>
      </div>

      <!-- Summary Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-semibold text-gray-900">Total Orders</h3>
              <p class="text-3xl font-bold text-blue-600 mt-1">{{ reportData.totalOrders }}</p>
              <p class="text-sm text-gray-600">{{ reportData.orderGrowth }}% from last period</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-semibold text-gray-900">Revenue</h3>
              <p class="text-3xl font-bold text-green-600 mt-1">{{ formatCurrency(reportData.totalRevenue) }}</p>
              <p class="text-sm text-gray-600">{{ reportData.revenueGrowth }}% from last period</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-semibold text-gray-900">Active Users</h3>
              <p class="text-3xl font-bold text-purple-600 mt-1">{{ reportData.activeUsers }}</p>
              <p class="text-sm text-gray-600">{{ reportData.userGrowth }}% from last period</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-8 w-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
              </svg>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-semibold text-gray-900">Avg Order Value</h3>
              <p class="text-3xl font-bold text-orange-600 mt-1">{{ formatCurrency(reportData.avgOrderValue) }}</p>
              <p class="text-sm text-gray-600">{{ reportData.avgOrderGrowth }}% from last period</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Charts and Tables -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Orders by Status -->
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Orders by Status</h3>
          <div class="space-y-4">
            <div v-for="status in ordersByStatus" :key="status.name" class="flex items-center justify-between">
              <div class="flex items-center">
                <div :class="['w-3 h-3 rounded-full mr-3', status.color]"></div>
                <span class="text-sm font-medium text-gray-900">{{ status.name }}</span>
              </div>
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">{{ status.count }}</span>
                <span class="text-xs text-gray-500">({{ status.percentage }}%)</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Top Selling Products -->
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Selling Products</h3>
          <div class="space-y-4">
            <div v-for="product in topProducts" :key="product.name" class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-900">{{ product.name }}</p>
                <p class="text-xs text-gray-500">{{ product.sales }} sales</p>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium text-gray-900">{{ formatCurrency(product.revenue) }}</p>
                <div class="w-20 bg-gray-200 rounded-full h-2 mt-1">
                  <div :class="['bg-primary-600 h-2 rounded-full']" :style="{ width: product.percentage + '%' }"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Detailed Tables -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Orders by Seller -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Orders by Seller</h3>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Seller</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Orders</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Rating</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="seller in sellerStats" :key="seller.name" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ seller.name }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ seller.orders }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatCurrency(seller.revenue) }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div class="flex items-center">
                      <span>{{ seller.rating.toFixed(1) }}</span>
                      <svg class="w-4 h-4 text-yellow-400 ml-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                      </svg>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Payment Status Overview -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Payment Status Overview</h3>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              <div v-for="payment in paymentStats" :key="payment.status" class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                  <div :class="['w-3 h-3 rounded-full mr-3', payment.color]"></div>
                  <span class="text-sm font-medium text-gray-900">{{ payment.status }}</span>
                </div>
                <div class="text-right">
                  <p class="text-lg font-bold text-gray-900">{{ payment.count }}</p>
                  <p class="text-sm text-gray-600">{{ formatCurrency(payment.amount) }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface ReportData {
  totalOrders: number
  orderGrowth: number
  totalRevenue: number
  revenueGrowth: number
  activeUsers: number
  userGrowth: number
  avgOrderValue: number
  avgOrderGrowth: number
}

const selectedPeriod = ref('month')

const reportData = ref<ReportData>({
  totalOrders: 1068,
  orderGrowth: 12.5,
  totalRevenue: 2450000,
  revenueGrowth: 8.3,
  activeUsers: 1247,
  userGrowth: 15.2,
  avgOrderValue: 22950,
  avgOrderGrowth: -2.1
})

const ordersByStatus = ref([
  { name: 'Delivered', count: 892, percentage: 83.5, color: 'bg-green-500' },
  { name: 'Approved', count: 145, percentage: 13.6, color: 'bg-blue-500' },
  { name: 'Pending', count: 23, percentage: 2.2, color: 'bg-yellow-500' },
  { name: 'Rejected', count: 8, percentage: 0.7, color: 'bg-red-500' }
])

const topProducts = ref([
  { name: 'Meru 12KG', sales: 245, revenue: 3675000, percentage: 100 },
  { name: 'K-Gas 6KG', sales: 189, revenue: 1512000, percentage: 77 },
  { name: 'Meru 25KG', sales: 156, revenue: 7020000, percentage: 64 },
  { name: 'K-Gas 12KG', sales: 134, revenue: 1876000, percentage: 55 },
  { name: 'Meru 6KG', sales: 98, revenue: 784000, percentage: 40 }
])

const sellerStats = ref([
  { name: 'Kigali Gas Supplies', orders: 234, revenue: 5265000, rating: 4.8 },
  { name: 'Rwanda Gas Pro', orders: 189, revenue: 4230000, rating: 4.6 },
  { name: 'Gas Express Rwanda', orders: 156, revenue: 3510000, rating: 4.7 },
  { name: 'Kigali Energy Solutions', orders: 123, revenue: 2760000, rating: 4.5 },
  { name: 'Rwanda Gas Network', orders: 98, revenue: 2205000, rating: 4.4 }
])

const paymentStats = ref([
  { status: 'Successful Payments', count: 1024, amount: 23040000, color: 'bg-green-500' },
  { status: 'Pending Payments', count: 32, amount: 720000, color: 'bg-yellow-500' },
  { status: 'Failed Payments', count: 12, amount: 270000, color: 'bg-red-500' }
])

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('rw-RW', {
    style: 'currency',
    currency: 'RWF',
    minimumFractionDigits: 0
  }).format(amount)
}

const exportReport = () => {
  // In a real app, this would generate and download a report
  alert(`Exporting ${selectedPeriod.value} report...`)
}

onMounted(() => {
  console.log('Admin reports loaded')
})
</script>
