<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-6">
          <h1 class="text-3xl font-bold text-gray-900">Browse Gas Products</h1>
          <p class="mt-2 text-gray-600">Find and order cooking gas from verified suppliers in your area</p>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Filters Section -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Filter Products</h2>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- Search -->
          <div>
            <label for="search" class="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <input
              id="search"
              v-model="filters.search"
              type="text"
              class="input-field"
              placeholder="Search by brand or type..."
            />
          </div>

          <!-- Brand Filter -->
          <div>
            <label for="brand" class="block text-sm font-medium text-gray-700 mb-1">
              Brand
            </label>
            <select id="brand" v-model="filters.brand" class="input-field">
              <option value="">All Brands</option>
              <option value="Meru">Meru</option>
              <option value="SP">SP</option>
              <option value="Total">Total</option>
              <option value="Oryx">Oryx</option>
            </select>
          </div>

          <!-- Weight Filter -->
          <div>
            <label for="weight" class="block text-sm font-medium text-gray-700 mb-1">
              Weight
            </label>
            <select id="weight" v-model="filters.weight" class="input-field">
              <option value="">All Weights</option>
              <option value="6kg">6 KG</option>
              <option value="12kg">12 KG</option>
              <option value="15kg">15 KG</option>
              <option value="22kg">22 KG</option>
            </select>
          </div>

          <!-- Location Filter -->
          <div>
            <label for="location" class="block text-sm font-medium text-gray-700 mb-1">
              Seller Location
            </label>
            <select id="location" v-model="filters.location" class="input-field">
              <option value="">All Locations</option>
              <option value="Gasabo">Gasabo</option>
              <option value="Kicukiro">Kicukiro</option>
              <option value="Nyarugenge">Nyarugenge</option>
            </select>
          </div>
        </div>

        <div class="mt-4 flex justify-between items-center">
          <button
            @click="clearFilters"
            class="text-sm text-gray-500 hover:text-gray-700 transition-colors"
          >
            Clear all filters
          </button>
          <button
            @click="applyFilters"
            class="btn-primary"
          >
            Apply Filters
          </button>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="text-center py-12">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <p class="mt-2 text-gray-600">Loading products...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="text-center py-12">
        <div class="text-red-600 mb-4">
          <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Error Loading Products</h3>
        <p class="text-gray-600 mb-4">{{ error }}</p>
        <button @click="loadProducts" class="btn-primary">Try Again</button>
      </div>

      <!-- Products Grid -->
      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <div
          v-for="product in filteredProducts"
          :key="product.id"
          class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200"
        >
          <!-- Product Image -->
          <div class="h-48 bg-gray-100 flex items-center justify-center">
            <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
          </div>

          <!-- Product Info -->
          <div class="p-4">
            <div class="flex items-center justify-between mb-2">
              <h3 class="text-lg font-semibold text-gray-900">{{ product.brand }}</h3>
              <span class="text-sm font-medium text-primary-600">{{ product.weight_kg }} KG</span>
            </div>

            <p class="text-2xl font-bold text-gray-900 mb-2">RWF {{ product.unit_price.toLocaleString() }}</p>

            <div class="text-sm text-gray-600 mb-3">
              <p class="font-medium">{{ product.seller_name }}</p>
              <p class="flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                {{ product.location }}
              </p>
            </div>

            <!-- Stock Status -->
            <div class="flex items-center justify-between mb-4">
              <span
                :class="[
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                  product.quantity > 0
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                ]"
              >
                {{ product.quantity > 0 ? 'In Stock' : 'Out of Stock' }}
              </span>
              <span class="text-sm text-gray-500">{{ product.quantity }} available</span>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-2">
              <button
                @click="addToCart(product)"
                :disabled="product.quantity === 0"
                class="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Add to Cart
              </button>
              <button
                @click="orderNow(product)"
                :disabled="product.quantity === 0"
                class="w-full btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Order Now
              </button>
            </div>
          </div>
        </div>
      </div>

        <!-- Empty State -->
        <div v-if="filteredProducts.length === 0" class="text-center py-12 col-span-full">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No products found</h3>
          <p class="mt-1 text-sm text-gray-500">Try adjusting your filters or search terms.</p>
        </div>
      </div>
    </div>

    <!-- Cart Summary (if items in cart) -->
    <div v-if="cartItems.length > 0" class="fixed bottom-4 right-4">
      <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm">
        <div class="flex items-center justify-between mb-2">
          <h3 class="font-semibold text-gray-900">Cart ({{ cartItems.length }})</h3>
          <button @click="viewCart" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
            View Cart
          </button>
        </div>
        <p class="text-sm text-gray-600">
          Total: RWF {{ cartTotal.toLocaleString() }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import apiService from '@/services/apiService'

const router = useRouter()

interface Product {
  id: number
  brand: string
  weight_kg: number
  unit_price: number
  seller_name: string
  location: string
  quantity: number
  seller: number
  date_added: string
  last_updated: string
}

const products = ref<Product[]>([])
const loading = ref(false)
const error = ref('')

const filters = reactive({
  search: '',
  brand: '',
  weight: '',
  location: ''
})

const cartItems = ref<Product[]>([])

const filteredProducts = computed(() => {
  return products.value.filter(product => {
    const matchesSearch = !filters.search ||
      product.brand.toLowerCase().includes(filters.search.toLowerCase()) ||
      product.seller_name.toLowerCase().includes(filters.search.toLowerCase())

    const matchesBrand = !filters.brand || product.brand === filters.brand
    const matchesWeight = !filters.weight || product.weight_kg.toString().includes(filters.weight)
    const matchesLocation = !filters.location || product.location === filters.location

    return matchesSearch && matchesBrand && matchesWeight && matchesLocation
  })
})

const cartTotal = computed(() => {
  return cartItems.value.reduce((total, item) => total + item.unit_price, 0)
})

// Load products from API
const loadProducts = async () => {
  loading.value = true
  error.value = ''

  try {
    const apiFilters: any = {}
    if (filters.brand) apiFilters.brand = filters.brand
    if (filters.weight) apiFilters.weight = parseFloat(filters.weight)
    if (filters.location) apiFilters.location = filters.location

    const data = await apiService.getGasInventory(apiFilters)
    products.value = data
  } catch (err: any) {
    error.value = err.message || 'Failed to load products'
    console.error('Error loading products:', err)
  } finally {
    loading.value = false
  }
}

const clearFilters = () => {
  filters.search = ''
  filters.brand = ''
  filters.weight = ''
  filters.location = ''
  loadProducts() // Reload products after clearing filters
}

const applyFilters = () => {
  loadProducts() // Reload products with new filters
}

const addToCart = (product: Product) => {
  cartItems.value.push(product)
  // In a real app, you'd also update the backend
}

const orderNow = (product: Product) => {
  // Add to cart and navigate to order page
  cartItems.value.push(product)
  router.push({ name: 'buyer-order' })
}

const viewCart = () => {
  router.push({ name: 'buyer-order' })
}

onMounted(() => {
  loadProducts()
})
</script>
