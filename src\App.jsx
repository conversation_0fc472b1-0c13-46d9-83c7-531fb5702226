import { Routes, Route } from 'react-router-dom'
import { AuthProvider } from './contexts/AuthContext'
import ProtectedRoute from './components/ProtectedRoute'

// Public pages
import LandingPage from './pages/LandingPage'
import LoginPage from './pages/auth/LoginPage'
import RegisterPage from './pages/auth/RegisterPage'

// Buyer pages
import BuyerDashboard from './pages/buyer/BuyerDashboard'
import BuyerOrders from './pages/buyer/BuyerOrders'
import BuyerOrder from './pages/buyer/BuyerOrder'
import BuyerRating from './pages/buyer/BuyerRating'

// Seller pages
import SellerDashboard from './pages/seller/SellerDashboard'
import SellerInventory from './pages/seller/SellerInventory'
import SellerOrders from './pages/seller/SellerOrders'
import SellerInvoice from './pages/seller/SellerInvoice'

// Admin pages
import AdminDashboard from './pages/admin/AdminDashboard'
import AdminOrders from './pages/admin/AdminOrders'
import AdminInvoices from './pages/admin/AdminInvoices'
import AdminUsers from './pages/admin/AdminUsers'
import AdminReports from './pages/admin/AdminReports'

function App() {
  return (
    <AuthProvider>
      <div className="min-h-screen bg-gray-50">
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={<LandingPage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/signup" element={<RegisterPage />} />

          {/* Buyer Routes */}
          <Route path="/buyer/dashboard" element={
            <ProtectedRoute allowedRoles={['buyer']}>
              <BuyerDashboard />
            </ProtectedRoute>
          } />
          <Route path="/buyer/order" element={
            <ProtectedRoute allowedRoles={['buyer']}>
              <BuyerOrder />
            </ProtectedRoute>
          } />
          <Route path="/buyer/orders" element={
            <ProtectedRoute allowedRoles={['buyer']}>
              <BuyerOrders />
            </ProtectedRoute>
          } />
          <Route path="/buyer/ratings/order/:id" element={
            <ProtectedRoute allowedRoles={['buyer']}>
              <BuyerRating />
            </ProtectedRoute>
          } />

          {/* Seller Routes */}
          <Route path="/seller/dashboard" element={
            <ProtectedRoute allowedRoles={['seller']}>
              <SellerDashboard />
            </ProtectedRoute>
          } />
          <Route path="/seller/inventory" element={
            <ProtectedRoute allowedRoles={['seller']}>
              <SellerInventory />
            </ProtectedRoute>
          } />
          <Route path="/seller/orders" element={
            <ProtectedRoute allowedRoles={['seller']}>
              <SellerOrders />
            </ProtectedRoute>
          } />
          <Route path="/seller/invoice/generate/:id" element={
            <ProtectedRoute allowedRoles={['seller']}>
              <SellerInvoice />
            </ProtectedRoute>
          } />

          {/* Admin Routes */}
          <Route path="/admin/dashboard" element={
            <ProtectedRoute allowedRoles={['admin']}>
              <AdminDashboard />
            </ProtectedRoute>
          } />
          <Route path="/admin/orders" element={
            <ProtectedRoute allowedRoles={['admin']}>
              <AdminOrders />
            </ProtectedRoute>
          } />
          <Route path="/admin/invoices" element={
            <ProtectedRoute allowedRoles={['admin']}>
              <AdminInvoices />
            </ProtectedRoute>
          } />
          <Route path="/admin/users" element={
            <ProtectedRoute allowedRoles={['admin']}>
              <AdminUsers />
            </ProtectedRoute>
          } />
          <Route path="/admin/reports" element={
            <ProtectedRoute allowedRoles={['admin']}>
              <AdminReports />
            </ProtectedRoute>
          } />
        </Routes>
      </div>
    </AuthProvider>
  )
}

export default App
