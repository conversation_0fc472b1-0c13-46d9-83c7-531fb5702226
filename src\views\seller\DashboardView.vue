<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-8">Seller Dashboard</h1>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900">Current Stock</h3>
          <p class="text-3xl font-bold text-primary-600 mt-2">45</p>
          <p class="text-sm text-gray-600">Gas cylinders</p>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900">Pending Orders</h3>
          <p class="text-3xl font-bold text-accent-600 mt-2">8</p>
          <p class="text-sm text-gray-600">Orders to process</p>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900">Completed Deliveries</h3>
          <p class="text-3xl font-bold text-secondary-600 mt-2">127</p>
          <p class="text-sm text-gray-600">This month</p>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div class="space-y-3">
            <router-link to="/seller/inventory" class="block btn-primary text-center">
              Manage Inventory
            </router-link>
            <router-link to="/seller/orders" class="block btn-secondary text-center">
              View Orders
            </router-link>
          </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
          <div class="space-y-3 text-sm">
            <div class="flex justify-between">
              <span>New order received</span>
              <span class="text-gray-500">2 hours ago</span>
            </div>
            <div class="flex justify-between">
              <span>Stock updated</span>
              <span class="text-gray-500">5 hours ago</span>
            </div>
            <div class="flex justify-between">
              <span>Order delivered</span>
              <span class="text-gray-500">1 day ago</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Seller dashboard logic will be implemented here
</script>
