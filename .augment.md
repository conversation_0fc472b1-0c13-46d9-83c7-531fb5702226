
###  Frontend Development Prompt: <PERSON><PERSON><PERSON> (React + Vite) – FINAL UPDATE WITH FULL API DOCS**

**Project Name:** <PERSON><PERSON><PERSON> GasGo (GasGod Rwanda)
**Frontend Framework:** React (with Vite)
**Styling:** Tailwind CSS
**Backend Repository:** [https://github.com/Alicelinzy/gas\_stock\_management\_backend](https://github.com/Alicelinzy/gas_stock_management_backend)
**Docker Backend:** Yes – the backend runs in Docker and exposes working API endpoints
**API Docs:** [Available Here](https://github.com/Alicelinzy/gas_stock_management_backend/blob/main/backend/docs/gas_management/api_documentation.md)

---

### 1. 🧩 Goal and Context

Rebuild the existing frontend (currently in Vue) **entirely using React + Vite**, integrating fully with the **backend APIs** from the GitHub repository. The frontend must support full user flows for **Buyers, Sellers, and Admins**, matching the API structure and behavior defined in the backend repo.

---

### 2. ⚙️ Tech Stack and Conventions

* React + Vite
* Tailwind CSS
* React Router v6
* Axios (with interceptors for JWT)
* JWT stored in localStorage
* Role-based routing (Admin, Seller, Buyer)
* Use `VITE_API_BASE_URL` for API endpoint root
* `react-hook-form` (optional) for forms
* Reusable UI components (e.g. `<OrderCard />`, `<Sidebar />`, etc.)

---

### 3. 👥 User Roles and Features

**Authentication (Public Pages):**

* `/` – Landing Page
* `/signup` – Register as Buyer or Seller
* `/login` – Login

**Buyer Dashboard:**

* `/buyer/dashboard` – Browse Products
* `/buyer/order` – Place Order
* `/buyer/orders` – View Orders
* `/buyer/ratings/order/:id` – Rate Seller

**Seller Dashboard:**

* `/seller/dashboard` – Stock + Order Overview
* `/seller/inventory` – Add or View Stock
* `/seller/orders` – View Orders
* `/seller/invoice/generate/:id` – Generate Invoice

**Admin Dashboard:**

* `/admin/dashboard` – Admin Overview
* `/admin/orders` – Approve Orders
* `/admin/invoices` – Approve Invoices
* `/admin/users` – Manage Users
* `/admin/reports` – Order Analytics

---

### 4. 🔁 API Integration

Use Axios to make all API calls. Add JWT tokens via interceptors. Handle loading states and errors gracefully.

---

### 🔐 **Authentication APIs**

#### `POST /api/accounts/login/`

```json
{
  "email": "<EMAIL>",
  "password": "yourpassword"
}
```

**Response:**

```json
{
  "access": "jwt_access_token",
  "refresh": "jwt_refresh_token"
}
```

#### `POST /api/accounts/register/`

```json
{
  "email": "<EMAIL>",
  "password": "yourpassword",
  "confirm_password": "yourpassword",
  "role": "buyer" // or "seller"
}
```

#### `POST /api/accounts/logout/`

**Header:** `Authorization: Bearer <token>`

---

### 🛒 **Buyer APIs**

#### `POST /api/buyer/orders/`

```json
{
  "product_id": 1,
  "quantity": 2,
  "delivery_address": "123 Kigali Street",
  "payment_method": "mtn_momo"
}
```

#### `GET /api/buyer/orders/`

#### `POST /api/buyer/ratings/`

```json
{
  "order_id": 101,
  "rating": 4,
  "comment": "Prompt delivery"
}
```

---

### 🏬 **Seller APIs**

#### `GET /api/seller/inventory/`

#### `POST /api/seller/inventory/`

```json
{
  "product_name": "Gas Cylinder 12kg",
  "quantity": 50,
  "price": 2500
}
```

#### `GET /api/seller/orders/`

#### `POST /api/seller/invoices/`

```json
{
  "order_id": 101,
  "amount": 5000
}
```

---

### 🛠️ **Admin APIs**

#### `GET /api/admin/orders/`

#### `POST /api/admin/orders/approve/`

```json
{
  "order_id": 101
}
```

#### `GET /api/admin/invoices/`

#### `POST /api/admin/invoices/approve/`

```json
{
  "invoice_id": 201
}
```

#### `GET /api/admin/users/`

---

### 5. 📱 UI/UX Guidelines

* Fully responsive
* Clear form validations
* Feedback for errors/success
* Loading spinners
* Use Tailwind utilities (`grid`, `flex`, breakpoints)

---

### 6. 📦 Final Instructions

* Replace Vue with a full React + Vite frontend
* Functional components only
* Docker backend will be used for testing
* Prioritize working features over polish
* Code structure: `/pages`, `/components`, `/services`, `/routes`

---

### 7. 🔗 Resources

* 🔥 **Backend API Repo:** [https://github.com/Alicelinzy/gas\_stock\_management\_backend](https://github.com/Alicelinzy/gas_stock_management_backend)
* 📖 **API Docs:** [https://github.com/Alicelinzy/gas\_stock\_management\_backend/blob/main/backend/docs/gas\_management/api\_documentation.md](https://github.com/Alicelinzy/gas_stock_management_backend/blob/main/backend/docs/gas_management/api_documentation.md)


