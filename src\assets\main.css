@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles with moderately larger fonts */
@layer base {
  html {
    font-family: "Inter", sans-serif;
    scroll-behavior: smooth;
    font-size: 17px; /* Slightly increased base font size from default 16px */
  }

  body {
    @apply bg-gray-50 text-gray-900 antialiased;
    font-size: 1rem; /* 17px base */
    line-height: 1.6; /* Better line spacing */
  }

  * {
    @apply border-gray-200;
  }

  /* Moderately increase heading sizes */
  h1 {
    @apply text-3xl font-bold; /* Moderately larger h1 */
  }

  h2 {
    @apply text-2xl font-semibold; /* Moderately larger h2 */
  }

  h3 {
    @apply text-xl font-semibold; /* Moderately larger h3 */
  }

  h4 {
    @apply text-lg font-medium; /* Moderately larger h4 */
  }

  /* Better paragraph spacing */
  p {
    @apply text-base leading-relaxed; /* Moderately larger paragraph text */
  }

  /* Moderately larger labels */
  label {
    @apply text-base font-medium; /* Moderately larger labels */
  }
}

/* Custom component styles with moderately larger fonts */
@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 text-base rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
  }

  .btn-secondary {
    @apply bg-white border-2 border-gray-200 hover:border-gray-300 text-gray-700 hover:text-gray-900 font-semibold py-3 px-6 text-base rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
  }

  .btn-accent {
    @apply bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold py-3 px-6 text-base rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
  }

  .input-field {
    @apply w-full px-4 py-3 text-base border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white;
  }

  .card {
    @apply bg-white rounded-2xl shadow-lg border border-gray-100 p-8 transition-all duration-300 hover:shadow-xl;
  }

  .card-header {
    @apply border-b border-gray-100 pb-6 mb-6;
  }

  .glass-card {
    @apply bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-6;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
  }

  /* Table styles with moderately larger fonts */
  .table-text {
    @apply text-base; /* Moderately larger table text */
  }

  .table-header {
    @apply text-base font-semibold; /* Moderately larger table headers */
  }

  /* Navigation styles */
  .nav-link {
    @apply text-base font-medium; /* Moderately larger navigation links */
  }

  /* Form styles */
  .form-label {
    @apply text-base font-medium mb-2; /* Moderately larger form labels */
  }

  .form-input {
    @apply text-base px-4 py-3; /* Moderately larger form inputs */
  }
}
