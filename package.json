{"name": "kigali-gasgo", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix"}, "dependencies": {"axios": "^1.9.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.28.0", "react-hook-form": "^7.53.2"}, "devDependencies": {"@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "autoprefixer": "^10.4.21", "postcss": "^8.5.4", "tailwindcss": "^3.4.0", "vite": "^6.2.4"}}