{"name": "kigali-gasgo", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "tsc --build", "lint": "eslint . --fix"}, "dependencies": {"axios": "^1.9.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.28.0", "react-hook-form": "^7.53.2"}, "devDependencies": {"@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/node": "^22.14.0", "@vitejs/plugin-react": "^4.3.4", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "eslint": "^9.22.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "autoprefixer": "^10.4.21", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "postcss": "^8.5.4", "tailwindcss": "^3.4.0", "typescript": "~5.8.0", "vite": "^6.2.4"}}