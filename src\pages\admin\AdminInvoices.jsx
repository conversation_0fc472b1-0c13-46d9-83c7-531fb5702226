import React, { useState, useEffect } from 'react'
import Layout from '../../components/Layout'
import apiService from '../../services/apiService'

const AdminInvoices = () => {
  const [invoices, setInvoices] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [processingInvoice, setProcessingInvoice] = useState(null)

  useEffect(() => {
    fetchInvoices()
  }, [])

  const fetchInvoices = async () => {
    try {
      setLoading(true)
      const invoicesData = await apiService.getAllInvoices()
      setInvoices(invoicesData)
    } catch (err) {
      setError(err.message || 'Failed to fetch invoices')
    } finally {
      setLoading(false)
    }
  }

  const handleApproveInvoice = async (invoiceId) => {
    try {
      setProcessingInvoice(invoiceId)
      await apiService.approveInvoice(invoiceId)
      // Refresh invoices list
      await fetchInvoices()
    } catch (err) {
      setError(err.message || 'Failed to approve invoice')
    } finally {
      setProcessingInvoice(null)
    }
  }

  const handleMarkAsPaid = async (invoiceId) => {
    try {
      setProcessingInvoice(invoiceId)
      await apiService.markInvoiceAsPaid(invoiceId)
      // Refresh invoices list
      await fetchInvoices()
    } catch (err) {
      setError(err.message || 'Failed to mark invoice as paid')
    } finally {
      setProcessingInvoice(null)
    }
  }

  const getStatusColor = (invoice) => {
    if (invoice.is_paid) {
      return 'bg-green-100 text-green-800'
    } else if (invoice.admin_approval) {
      return 'bg-blue-100 text-blue-800'
    } else {
      return 'bg-yellow-100 text-yellow-800'
    }
  }

  const getStatusText = (invoice) => {
    if (invoice.is_paid) {
      return 'Paid'
    } else if (invoice.admin_approval) {
      return 'Approved'
    } else {
      return 'Pending Approval'
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Invoice Management</h1>
          <p className="mt-2 text-gray-600">Review and approve seller invoices</p>
        </div>

        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
            <button
              onClick={fetchInvoices}
              className="ml-2 text-red-800 hover:text-red-900 underline"
            >
              Try again
            </button>
          </div>
        )}

        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">All Invoices</h2>
          </div>

          {invoices.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              No invoices found.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Invoice ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Order ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Seller
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {invoices.map((invoice) => (
                    <tr key={invoice.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        #{invoice.id}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        #{invoice.order?.id || invoice.order_id || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {invoice.seller_email || invoice.seller?.email || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        RWF {invoice.total_amount?.toLocaleString() || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {invoice.created_at ? new Date(invoice.created_at).toLocaleDateString() : 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(invoice)}`}>
                          {getStatusText(invoice)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        {!invoice.admin_approval && (
                          <button
                            onClick={() => handleApproveInvoice(invoice.id)}
                            disabled={processingInvoice === invoice.id}
                            className="text-green-600 hover:text-green-900 disabled:opacity-50 mr-3"
                          >
                            {processingInvoice === invoice.id ? 'Processing...' : 'Approve'}
                          </button>
                        )}
                        {invoice.admin_approval && !invoice.is_paid && (
                          <button
                            onClick={() => handleMarkAsPaid(invoice.id)}
                            disabled={processingInvoice === invoice.id}
                            className="text-blue-600 hover:text-blue-900 disabled:opacity-50"
                          >
                            {processingInvoice === invoice.id ? 'Processing...' : 'Mark as Paid'}
                          </button>
                        )}
                        {invoice.is_paid && (
                          <span className="text-gray-400">Completed</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </Layout>
  )
}

export default AdminInvoices
