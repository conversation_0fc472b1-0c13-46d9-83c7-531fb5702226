/* Custom styles for GasGo Rwanda */

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Button styles */
.btn-primary {
  @apply inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-semibold rounded-xl text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl;
}

.btn-secondary {
  @apply inline-flex items-center justify-center px-6 py-3 border-2 border-blue-600 text-base font-semibold rounded-xl text-blue-600 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg;
}

/* Navigation link styles */
.nav-link {
  @apply px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200;
}

/* Card styles */
.card {
  @apply bg-white rounded-2xl shadow-xl p-8 border border-gray-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1;
}

.card-header {
  @apply mb-6 pb-6 border-b border-gray-100;
}

/* Form styles */
.form-label {
  @apply block text-sm font-semibold text-gray-700 mb-2;
}

.input-field {
  @apply w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white shadow-sm;
}

/* Hover effects for cards */
.card:hover {
  transform: translateY(-4px);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design helpers */
@media (max-width: 640px) {
  .card {
    @apply p-6;
  }
  
  .btn-primary,
  .btn-secondary {
    @apply px-4 py-2 text-sm;
  }
}

/* Focus states for accessibility */
.btn-primary:focus,
.btn-secondary:focus,
.input-field:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Loading spinner */
.spinner {
  @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
}

/* Success and error states */
.success {
  @apply bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-xl;
}

.error {
  @apply bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl;
}

/* Hero section background */
.hero-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Feature icons */
.feature-icon {
  @apply flex items-center justify-center h-12 w-12 rounded-xl text-white mx-auto;
}

/* Pricing card special styling */
.pricing-card {
  @apply bg-white rounded-2xl shadow-xl p-8 border border-gray-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2;
}

.pricing-card.popular {
  @apply border-2 border-blue-500 relative;
}

.pricing-badge {
  @apply absolute -top-3 left-1/2 transform -translate-x-1/2 px-4 py-1 text-sm font-semibold text-white bg-gradient-to-r from-blue-600 to-purple-600 rounded-full;
}

/* Footer styling */
.footer-link {
  @apply text-gray-300 hover:text-white transition-colors duration-200;
}

/* Social media icons */
.social-icon {
  @apply text-gray-400 hover:text-white transition-colors duration-200 transform hover:scale-110;
}

/* Navigation improvements */
.nav-container {
  @apply bg-white/95 backdrop-blur-sm border-b border-gray-200;
}

/* Mobile menu */
.mobile-menu {
  @apply md:hidden bg-white border-t border-gray-200 px-4 py-2;
}

/* Stats display */
.stat-card {
  @apply text-center p-6 bg-white rounded-xl shadow-md;
}

.stat-number {
  @apply text-3xl font-bold;
}

.stat-label {
  @apply text-gray-600 mt-2;
}
